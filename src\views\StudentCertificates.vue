<template>
  <StudentLayoutWrapper contentClass="bg-gray-50">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Certificates</h1>
      <p class="text-gray-600 mt-1">View and download your earned certificates</p>
    </div>

    <!-- Enhanced Filters and Search -->
    <div class="bg-white p-4 sm:p-6 rounded-lg shadow-sm mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- Improved Search with animation -->
        <div class="relative flex-grow">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search certificates..."
            class="w-full pl-11 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
          />
          <div class="absolute left-0 top-0 h-full flex items-center pl-4">
            <svg 
              class="h-5 w-5 text-gray-400 transition-colors duration-200"
              :class="{'text-orange-500': searchQuery}"
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <button 
            v-if="searchQuery"
            @click="searchQuery = ''"
            class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Enhanced Sort Dropdown -->
        <div class="relative">
          <button
            @click="toggleSortOptions"
            class="flex items-center justify-between w-full sm:w-48 px-4 py-3 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
          >
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
              </svg>
              {{ sortOptions[sortBy] }}
            </span>
            <svg 
              class="h-5 w-5 text-gray-400 transition-transform duration-200"
              :class="{ 'rotate-180': sortOptionsOpen }"
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Animated Sort Dropdown -->
          <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="sortOptionsOpen"
              class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-10"
            >
              <div class="py-1">
                <button
                  v-for="(label, value) in sortOptions"
                  :key="value"
                  @click="setSortBy(value)"
                  class="w-full px-4 py-3 text-left text-sm hover:bg-orange-50 transition-colors duration-150"
                  :class=" [
                    sortBy === value 
                      ? 'text-orange-600 bg-orange-50 font-medium' 
                      : 'text-gray-700'
                  ]"
                >
                  {{ label }}
                </button>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>

    <!-- Certificate Grid -->
    <div 
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
      v-if="!isLoading"
    >
      <div
        v-for="certificate in filteredCertificates"
        :key="certificate.id"
        class="group bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-lg"
      >
        <!-- Certificate Preview -->
        <div
          class="relative h-48 bg-cover bg-center transition-transform duration-300"
          :style="{ backgroundImage: certificate.image ? `url(${certificate.image})` : certificate.gradient }"
        >
          <div class="absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 group-hover:bg-opacity-10"></div>
          <div class="absolute bottom-4 left-4 right-4">
            <span 
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 shadow-sm"
            >
              {{ certificate.category }}
            </span>
          </div>
        </div>

        <!-- Certificate Info -->
        <div class="p-5">
          <h4 class="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors duration-200">
            {{ certificate.title }}
          </h4>
          <p class="text-sm text-gray-500 mb-4">Issued on {{ certificate.issueDate }}</p>
          
          <!-- Action Buttons -->
          <div class="flex items-center justify-between">
            <button
              @click="viewCertificate(certificate)"
              class="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg text-sm font-medium hover:bg-orange-700 focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View
            </button>
            <span class="flex items-center text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Verified
            </span>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="filteredCertificates.length === 0" class="bg-white p-8 rounded-lg shadow-sm text-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No certificates found</h3>
      <p class="text-gray-600 mb-4">
        {{ searchQuery ? 'Try adjusting your search query' : 'Complete courses to earn certificates' }}
      </p>
      <button
        @click="resetFilters"
        v-if="searchQuery"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
      >
        Clear Search
      </button>
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6 mb-8">
      <nav class="flex items-center space-x-2" aria-label="Pagination">
        <button
          @click="currentPage > 1 && (currentPage--)"
          :disabled="currentPage === 1"
          class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <div v-for="page in totalPages" :key="page" class="hidden md:block">
          <button
            @click="currentPage = page"
            :class="{
              'px-3 py-1 rounded-md text-sm font-medium': true,
              'bg-orange-600 text-white': currentPage === page,
              'border border-gray-300 text-gray-700 hover:bg-gray-50': currentPage !== page
            }"
          >
            {{ page }}
          </button>
        </div>
        <span class="md:hidden text-sm text-gray-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          @click="currentPage < totalPages && (currentPage++)"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </nav>
    </div>

    <!-- Certificate View Modal -->
    <div v-if="showCertificateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
        <div class="p-6 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-800">Certificate of Completion</h3>
          <button @click="showCertificateModal = false" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-6">
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
            <div class="mb-6">
              <img src="/logo.png" alt="Flow Camp" class="h-12 mx-auto mb-2" />
              <h2 class="text-2xl font-bold text-gray-800">Flow Camp</h2>
            </div>
            <div class="mb-6">
              <h1 class="text-3xl font-bold text-gray-900 mb-2">Certificate of Completion</h1>
              <p class="text-gray-600">This certifies that</p>
              <p class="text-xl font-semibold text-gray-800 my-2">{{ studentName }}</p>
              <p class="text-gray-600">has successfully completed</p>
              <p class="text-xl font-semibold text-gray-800 my-2">{{ selectedCertificate?.title }}</p>
              <p class="text-gray-600">on {{ selectedCertificate?.issueDate }}</p>
            </div>
            <div class="flex justify-center space-x-8 mt-8">
              <div class="text-center">
                <div class="w-32 h-px bg-gray-300 mx-auto mb-2"></div>
                <p class="text-gray-600">Instructor Signature</p>
              </div>
              <div class="text-center">
                <div class="w-32 h-px bg-gray-300 mx-auto mb-2"></div>
                <p class="text-gray-600">Director Signature</p>
              </div>
            </div>
            <div class="mt-8">
              <p class="text-sm text-gray-500">Certificate ID: {{ selectedCertificate?.id }}</p>
              <p class="text-sm text-gray-500">Verify at flowcamp.id/verify</p>
            </div>
          </div>
          <div class="mt-6 flex justify-center">
            <button
              @click="downloadCertificate"
              class="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              Download Certificate
            </button>
          </div>
        </div>
      </div>
    </div>
  </StudentLayoutWrapper>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import StudentLayoutWrapper from '@/components/@student/StudentLayoutWrapper.vue';
import { useClassStore } from '@/data/availableClasses';

const classStore = useClassStore();

// Loading state
const isLoading = ref(true);

// Pagination
const currentPage = ref(1);
const itemsPerPage = 6;

// Filters and sorting
const searchQuery = ref('');
const sortBy = ref('recent');
const sortOptionsOpen = ref(false);
const sortOptions = {
  recent: 'Most Recent',
  oldest: 'Oldest First',
  alphabetical: 'Alphabetical'
};

// Certificate modal
const showCertificateModal = ref(false);
const selectedCertificate = ref(null);
const studentName = ref('Student Name'); // In a real app, this would come from user profile

// Toggle sort options dropdown
const toggleSortOptions = () => {
  sortOptionsOpen.value = !sortOptionsOpen.value;
};

// Set sort option
const setSortBy = (option) => {
  sortBy.value = option;
  // Add slight delay before closing dropdown for smooth animation
  setTimeout(() => {
    sortOptionsOpen.value = false;
  }, 100);
};

// Reset filters
const resetFilters = () => {
  searchQuery.value = '';
  sortBy.value = 'recent';
  currentPage.value = 1;
};

// Close dropdown when clicking outside
const closeDropdownOnClickOutside = (event) => {
  if (sortOptionsOpen.value && !event.target.closest('.relative')) {
    sortOptionsOpen.value = false;
  }
};

// View certificate
const viewCertificate = (certificate) => {
  selectedCertificate.value = certificate;
  showCertificateModal.value = true;
};

// Add new reactive refs for download state
const isDownloading = ref(false);
const downloadProgress = ref(0);

// Enhanced download function with progress
const downloadCertificate = async () => {
  if (!selectedCertificate.value) return;
  
  isDownloading.value = true;
  downloadProgress.value = 0;

  try {
    // Simulate download progress
    for (let i = 0; i <= 100; i += 10) {
      downloadProgress.value = i;
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Success handling here
    showCertificateModal.value = false;
    // Show success toast or notification
  } catch (error) {
    console.error('Download failed:', error);
    // Show error toast or notification
  } finally {
    isDownloading.value = false;
    downloadProgress.value = 0;
  }
};

// Generate certificates from completed classes
const certificates = computed(() => {
  const certificatesList = [];
  const classes = classStore.classes?.value || [];
  
  // Find completed classes
  const completedClasses = classes.filter(cls => cls.progress === 100);
  
  completedClasses.forEach((cls, index) => {
    // Generate a completion date if not available
    const completionDate = cls.completionDate || new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    certificatesList.push({
      id: `CERT-${cls.id}-${Date.now()}`,
      title: cls.title,
      category: cls.category || 'Web Development',
      issueDate: completionDate,
      image: cls.imageUrl || null,
      gradient: getGradient(index)
    });
  });
  
  return certificatesList;
});

// Helper function to get gradient background for certificates without images
const getGradient = (index) => {
  const gradients = [
    'linear-gradient(to right, #c471f5, #fa71cd)',
    'linear-gradient(to right, #4facfe, #00f2fe)',
    'linear-gradient(to right, #ff8177, #ff867a)',
    'linear-gradient(to right, #fbc2eb, #a6c1ee)',
    'linear-gradient(to right, #84fab0, #8fd3f4)',
    'linear-gradient(to right, #a1c4fd, #c2e9fb)'
  ];
  return gradients[index % gradients.length];
};

// Filter and sort certificates
const filteredCertificates = computed(() => {
  const query = searchQuery.value.toLowerCase();
  
  // Filter by search query
  let filtered = certificates.value.filter(cert => 
    cert.title.toLowerCase().includes(query) ||
    cert.category.toLowerCase().includes(query)
  );
  
  // Sort based on selected option
  if (sortBy.value === 'recent') {
    filtered = [...filtered].sort((a, b) => new Date(b.issueDate) - new Date(a.issueDate));
  } else if (sortBy.value === 'oldest') {
    filtered = [...filtered].sort((a, b) => new Date(a.issueDate) - new Date(b.issueDate));
  } else if (sortBy.value === 'alphabetical') {
    filtered = [...filtered].sort((a, b) => a.title.localeCompare(b.title));
  }
  
  // Calculate total pages
  const totalPages = Math.ceil(filtered.length / itemsPerPage);
  if (currentPage.value > totalPages && totalPages > 0) {
    currentPage.value = Math.max(1, totalPages);
  }
  
  // Get paginated results
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filtered.slice(startIndex, startIndex + itemsPerPage);
});

// Calculate total pages
const totalPages = computed(() => {
  const query = searchQuery.value.toLowerCase();
  
  const filtered = certificates.value.filter(cert => 
    cert.title.toLowerCase().includes(query) ||
    cert.category.toLowerCase().includes(query)
  );
  
  return Math.ceil(filtered.length / itemsPerPage) || 1;
});

// Lifecycle hooks
onMounted(() => {
  // Add click event listener for closing dropdown
  document.addEventListener('click', closeDropdownOnClickOutside);
  
  // Simulate loading data
  setTimeout(() => {
    isLoading.value = false;
    
    // Set student name from user data if available
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        if (user.name) {
          studentName.value = user.name;
        }
      } catch (e) {
        console.error('Error parsing user data:', e);
      }
    }
  }, 800);
});

onUnmounted(() => {
  // Remove event listener
  document.removeEventListener('click', closeDropdownOnClickOutside);
});
</script>

<style scoped>
.scroll-smooth {
  scroll-behavior: smooth;
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
  background-size: 1000px 100%;
}

.grid {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.grid > * {
  backface-visibility: hidden;
  transform-origin: center center;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
