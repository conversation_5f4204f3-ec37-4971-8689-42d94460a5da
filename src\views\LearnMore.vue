<template>
  <div>
    <!-- Hero Section -->
    <HeroSection
      badge-text="Bootcamp"
      title-text="Join Our Bootcamp and Shape Your Digital Future"
      subtitle-text="Boost Your Skills with Our Intensive Bootcamp — From Beginner to Job-Ready!"
      description-text="Ready to grow?"
      whatsappText="Try Free Consultation"
      :hero-image="headerImage"
      :use-overlay="true"
      :is-dark="true"
      :hide-right-section="true"
    />

    <!-- Section: Master Your Skills -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4 flex flex-col md:flex-row items-center gap-8">
        <div class="md:w-1/2 flex justify-center mb-8 md:mb-0">
          <img src="/LearnPic.png" alt="Master Your Skills" class="max-w-full h-auto" />
        </div>
        <div class="md:w-1/2">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">Master Your Skills with a Structured Bootcamp Curriculum</h2>
          <h3 class="text-xl font-bold mb-2">Comprehensive Curriculum With Hands-On Experience</h3>
          <p class="text-gray-600 mb-4">Our bootcamp offers a structured learning path with real-world projects, expert mentorship, and industry-relevant skills that will prepare you for a successful tech career.</p>
          <ul class="space-y-2">
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              Project-based learning with practical applications and real-world scenarios
            </li>
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              One-on-one mentorship from industry professionals with years of experience
            </li>
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              Small class sizes ensuring personalized attention and feedback
            </li>
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              Career support including portfolio development and job placement assistance
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- What You'll Learn Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">What You'll Learn?</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Week 1 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="01"
              gradient-id="week1-gradient"
              :gradient-colors="['#FFB43A', '#FF5A1F']"
            />
            <h3 class="text-lg font-bold text-[#FFB43A] mb-2 mt-4">Introduction Nest JS</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Understanding Node.js and TypeScript basics</li>
              <li>Setting up a new Nest JS project</li>
              <li>Exploring the project structure (modules, controllers, services)</li>
            </ul>
          </div>
          <!-- Week 2 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="02"
              gradient-id="week2-gradient"
              :gradient-colors="['#FF9900', '#FF5A1F']"
            />
            <h3 class="text-lg font-bold text-[#FF9900] mb-2 mt-4">Building Rest APIs</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Defining routes and handling HTTP requests</li>
              <li>Implementing CRUD operations</li>
              <li>Testing API endpoints with Postman</li>
            </ul>
          </div>
          <!-- Week 3 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="03"
              gradient-id="week3-gradient"
              :gradient-colors="['#1CC8EE', '#007CF0']"
            />
            <h3 class="text-lg font-bold text-[#1CC8EE] mb-2 mt-4">Database Integration</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Introduction to databases SQL</li>
              <li>Connecting Nest JS to a PostgreSQL database using TypeORM</li>
              <li>Querying data</li>
            </ul>
          </div>
          <!-- Week 4 -->
          <div class="flex flex-col items-center text-center">
            <CircleWeek
              week="04"
              gradient-id="week4-gradient"
              :gradient-colors="['#FF5A5F', '#FFB43A']"
            />
            <h3 class="text-lg font-bold text-[#FF5A5F] mb-2 mt-4">Authentication</h3>
            <ul class="text-gray-700 text-sm list-decimal list-inside text-left">
              <li>Implementing JWT (JSON Web Token) authentication</li>
              <li>Creating login and registration endpoints</li>
              <li>Protecting routes with guards</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-4">
          <span class="text-teal-600 font-bold tracking-widest text-sm">PRICING</span>
        </div>
        <h2 class="text-4xl font-bold text-center mb-4">Affordable for you, valuable for your future</h2>
        <p class="text-gray-500 text-center mb-4 max-w-2xl mx-auto">Invest in your skills with our flexible pricing options designed to fit your budget while providing exceptional value. Our bootcamps offer industry-recognized training at competitive rates.</p>

        <!-- Back to Program Button -->
        <div class="flex justify-start mb-6 max-w-4xl mx-auto">
          <router-link to="/program" class="inline-flex items-center text-gray-600 hover:text-gray-900 font-medium px-4 py-2 rounded transition">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Program
          </router-link>
        </div>
        <div class="flex flex-col md:flex-row items-center justify-center gap-8 max-w-4xl mx-auto bg-[#FAFAFD] rounded-2xl shadow p-8">
          <!-- Left: Info -->
          <div class="flex-1 flex flex-col justify-between items-start w-full md:w-1/2">
            <!-- Program Icon & Title -->
            <div class="flex items-center mb-2">
              <img
                :src="program && program.type === 'frontend' ? '/icons/laptop.svg' :
                     program && program.type === 'backend' ? '/icons/server.svg' :
                     program && program.type === 'mobile' ? '/icons/phone.svg' : '/NestJS.png'"
                :alt="program ? program.title : 'Program'"
                class="w-10 h-10 mr-2"
              />
              <span class="text-2xl font-bold">{{ program ? program.title : 'Program' }}</span>
            </div>
            <p class="text-gray-500 mb-4" v-if="currentMentor">Learn from {{ currentMentor.name }}, a {{ currentMentor.role }} at {{ currentMentor.company }}, and level up your skills with expert guidance.</p>

            <!-- Mentor Bio -->
            <p class="text-gray-600 text-sm mb-4" v-if="currentMentor && currentMentor.bio">
              {{ currentMentor.bio }}
            </p>

            <!-- Price and Duration -->
            <div class="mb-6" v-if="currentMentor">
              <span class="text-3xl font-bold text-black">Rp {{ currentMentor.price ? currentMentor.price.toLocaleString() : '600.000' }}</span>
              <span class="text-gray-400 text-lg font-medium">/{{ currentMentor.duration ? currentMentor.duration.split(' ')[1] || 'Month' : 'Month' }}</span>
            </div>

            <button class="bg-[#006D77] hover:bg-[#00535a] text-white font-semibold rounded-lg px-8 py-3 text-lg shadow transition focus:outline-none focus:ring-2 focus:ring-[#006D77] active:scale-95">Book Class</button>
          </div>

          <!-- Right: Mentor Photo -->
          <div class="flex-1 flex flex-col items-center w-full md:w-1/2">
            <div class="relative w-full max-w-xs rounded-2xl overflow-hidden shadow mb-4">
              <img :src="currentMentor ? currentMentor.image : 'https://randomuser.me/api/portraits/women/44.jpg'" :alt="currentMentor ? currentMentor.name : 'Mentor'" class="w-full h-56 object-cover" />
              <span class="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full">{{ currentMentor ? currentMentor.name : 'Mentor' }}</span>
            </div>

            <!-- Mentor Details Badge -->
            <div class="flex flex-col gap-2 w-full max-w-xs" v-if="currentMentor">
              <!-- Role and Company -->
              <div class="flex items-center justify-between bg-white rounded-full px-4 py-2 shadow text-xs font-medium">
                <!-- Role (left-aligned) -->
                <span class="inline-flex items-center gap-1">
                  <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  {{ currentMentor.role }}
                </span>
                <!-- Company (right-aligned) -->
                <span class="inline-flex items-center gap-1 ml-auto">
                  <img
                    :src="getCompanyIcon(currentMentor.company)"
                    :alt="currentMentor.company"
                    class="w-4 h-4 object-contain"
                  />
                  {{ currentMentor.company }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { programs } from '@/data/programs';
import HeroSection from '@/components/HeroSection.vue';
import CircleWeek from '@/components/CircleWeek.vue';
const headerImage = '/header.jpg';

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const program = ref(null);
const mentorIndex = ref(0);

// Function to select a mentor and update the URL
const selectMentor = (index) => {
  if (index >= 0 && index < mentorsList.value.length) {
    // Update the URL to reflect the selected mentor
    router.push(`/learn-more/${program.value.id}/${index}`);
    // The mentorIndex will be updated by the watcher
  }
};

// Function to get the appropriate company icon based on company name
const getCompanyIcon = (company) => {
  if (!company) return '/WhiteGoogle.png';

  const companyLower = company.toLowerCase();

  // Tech companies
  if (companyLower.includes('google')) {
    return '/WhiteGoogle.png';
  } else if (companyLower.includes('meta') || companyLower.includes('facebook')) {
    return '/meta-icon.jpg';
  } else if (companyLower.includes('apple')) {
    return '/apple-icon.png';
  } else if (companyLower.includes('microsoft')) {
    return '/microsoft-icon.png';
  } else if (companyLower.includes('vercel')) {
    return '/vercel-icon.jpg';
  } else if (companyLower.includes('tailwind')) {
    return '/tailwind-icon.png';
  }

  // Additional companies
  else if (companyLower.includes('github')) {
    return '/github-icon.png';
  } else if (companyLower.includes('airbnb')) {
    return '/airbnb-icon.png';
  } else if (companyLower.includes('figma')) {
    return '/figma-icon.png';
  } else if (companyLower.includes('stripe')) {
    return '/stripe-icon.png';
  } else if (companyLower.includes('netlify')) {
    return '/netlify-icon.png';
  } else if (companyLower.includes('cloudflare')) {
    return '/cloudflare-icon.png';
  } else if (companyLower.includes('uber')) {
    return '/uber-icon.png';
  }

  // Default icon for other companies
  return '/WhiteGoogle.png';
};

// Get the list of mentors for the current program
const mentorsList = computed(() => {
  if (!program.value) return [];

  if (program.value.mentors && Array.isArray(program.value.mentors)) {
    // Use the enhanced mentor data directly from the programs.js file
    return program.value.mentors;
  } else if (program.value.mentor) {
    // For backward compatibility with older data structure
    return [{
      id: 1,
      name: program.value.mentor,
      image: program.value.mentorImage,
      price: program.value.price,
      duration: program.value.duration,
      role: 'Senior Developer',
      company: 'Tech Company',
      bio: `Experienced ${program.value.title} developer with expertise in building modern applications.`
    }];
  }
  return [];
});

// Get the current mentor based on the mentor index
const currentMentor = computed(() => {
  if (!program.value || mentorsList.value.length === 0) return null;

  // Use the mentor index to get the specific mentor
  const index = mentorIndex.value < mentorsList.value.length ? mentorIndex.value : 0;
  return mentorsList.value[index];
});

// Watch for route changes to update the mentor index
watch(() => route.params.mentorIndex, (newMentorIndex) => {
  if (newMentorIndex !== undefined) {
    mentorIndex.value = parseInt(newMentorIndex);
  } else {
    mentorIndex.value = 0;
  }
}, { immediate: true });

onMounted(() => {
  const programId = parseInt(route.params.id);
  program.value = programs.find(p => p.id === programId);

  // Set the mentor index from the route parameter if available
  if (route.params.mentorIndex !== undefined) {
    mentorIndex.value = parseInt(route.params.mentorIndex);
  }

  loading.value = false;
});
</script>
